#!/usr/bin/env node

// Test script to verify plugin deletion functionality
import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5001';

// You'll need to get a valid JWT token from the browser's localStorage or login
const TEST_TOKEN = 'your-jwt-token-here'; // Replace with actual token

async function testPluginDeletion() {
  console.log('🧪 Testing Plugin Deletion Functionality');
  console.log('=' .repeat(60));
  
  try {
    // Step 1: Get current added plugins
    console.log('\n📋 Getting current added plugins...');
    
    const getResponse = await fetch(`${BASE_URL}/api/plugins/added`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    if (!getResponse.ok) {
      console.log('❌ Failed to get added plugins:', getResponse.status);
      const errorData = await getResponse.json();
      console.log('Error:', errorData.message);
      return;
    }
    
    const getResult = await getResponse.json();
    console.log('✅ Successfully retrieved added plugins');
    console.log(`📊 Total plugins: ${getResult.addedPlugins.length}`);
    
    if (getResult.addedPlugins.length === 0) {
      console.log('⚠️ No plugins found to test deletion');
      return;
    }
    
    // Show first few plugins
    console.log('\n📦 Available plugins:');
    getResult.addedPlugins.slice(0, 3).forEach((plugin, index) => {
      console.log(`${index + 1}. ${plugin.displayName || plugin.pluginName} (slug: ${plugin.pluginSlug})`);
    });
    
    // Step 2: Test deletion of the first plugin
    const testPlugin = getResult.addedPlugins[0];
    console.log(`\n🗑️ Testing deletion of plugin: ${testPlugin.displayName || testPlugin.pluginName}`);
    console.log(`   Slug: ${testPlugin.pluginSlug}`);
    
    const deleteResponse = await fetch(`${BASE_URL}/api/plugins/added/${testPlugin.pluginSlug}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!deleteResponse.ok) {
      console.log('❌ Failed to delete plugin:', deleteResponse.status);
      const errorData = await deleteResponse.json();
      console.log('Error:', errorData.message);
      return;
    }
    
    const deleteResult = await deleteResponse.json();
    console.log('Delete Response:', deleteResult.success ? '✅ Success' : '❌ Failed');
    if (deleteResult.message) {
      console.log('Message:', deleteResult.message);
    }
    
    // Step 3: Verify the plugin is no longer in the active list
    console.log('\n🔍 Verifying plugin is removed from active list...');
    
    const verifyResponse = await fetch(`${BASE_URL}/api/plugins/added`, {
      headers: {
        'Authorization': `Bearer ${TEST_TOKEN}`
      }
    });
    
    const verifyResult = await verifyResponse.json();
    const stillExists = verifyResult.addedPlugins.find(p => p.pluginSlug === testPlugin.pluginSlug);
    
    if (stillExists) {
      console.log('❌ Plugin still exists in active list - DELETION FAILED');
      console.log('Plugin data:', stillExists);
    } else {
      console.log('✅ Plugin successfully removed from active list');
    }
    
    console.log(`\n📊 Plugins count after deletion: ${verifyResult.addedPlugins.length}`);
    console.log('🎉 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
  }
}

// Instructions for getting the JWT token
console.log('📝 Instructions:');
console.log('1. Open the browser and login to the dashboard');
console.log('2. Open browser DevTools (F12)');
console.log('3. Go to Application/Storage tab > Local Storage');
console.log('4. Copy the "token" value');
console.log('5. Replace TEST_TOKEN in this script with the actual token');
console.log('6. Run this script again');
console.log('');

// Uncomment the line below and add your token to run the test
// testPluginDeletion();
