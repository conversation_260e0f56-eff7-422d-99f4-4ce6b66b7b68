import express from "express";
import fetch from "node-fetch";
import Plugin from "../models/Plugin.js";
import PluginRankHistory from "../models/PluginRankHistory.js";
import AddedPlugin from "../models/AddedPlugin.js";
import PluginDownloadData from "../models/PluginDownloadData.js";
import PluginReview from "../models/PluginReview.js";
import PluginKeyword from "../models/PluginKeyword.js";
import PluginKeywordRank from "../models/PluginKeywordRank.js";
import PluginVersion from "../models/PluginVersion.js";
import PluginInformation from "../models/PluginInformation.js";
import Settings from "../models/Settings.js";
import { authenticateToken, requireAdmin } from "../middleware/auth.js";
import logger from "../utils/logger.js";

const router = express.Router();

// Utility function to fetch download data for a plugin
async function fetchPluginDownloadData(pluginSlug, pluginName) {
  try {
    console.log(`Fetching download data for plugin: ${pluginSlug}`);

    const downloadUrl = `https://api.wordpress.org/stats/plugin/1.0/downloads.php?slug=${pluginSlug}`;
    const response = await fetch(downloadUrl);

    if (!response.ok) {
      console.log(
        `Failed to fetch download data for ${pluginSlug}: ${response.status}`
      );
      return null;
    }

    const downloadData = await response.json();

    if (!downloadData || typeof downloadData !== "object") {
      console.log(`Invalid download data format for ${pluginSlug}`);
      return null;
    }

    // Store download data using upsert
    const result = await PluginDownloadData.upsertDownloadData(
      pluginSlug,
      pluginName,
      downloadData
    );

    console.log(`Successfully stored download data for ${pluginSlug}`);
    return result;
  } catch (error) {
    console.error(`Error fetching download data for ${pluginSlug}:`, error);
    return null;
  }
}

// Utility function to clean and decode content
function cleanAndDecodeContent(rawContent) {
  if (!rawContent) return "";

  // Remove CDATA markers
  let cleaned = rawContent
    .replace(/<!\[CDATA\[/g, "")
    .replace(/\]\]>/g, "")
    .trim();

  // Decode HTML entities - use server-safe approach
  // Since we're in a Node.js environment, we can't use document.createElement
  // Use comprehensive entity decoding for server environment
  cleaned = cleaned
    .replace(/&amp;/g, "&")
    .replace(/&lt;/g, "<")
    .replace(/&gt;/g, ">")
    .replace(/&quot;/g, '"')
    .replace(/&#039;/g, "'")
    .replace(/&#8217;/g, "'") // Right single quotation mark
    .replace(/&#8211;/g, "–") // En dash
    .replace(/&#8212;/g, "—") // Em dash
    .replace(/&#8230;/g, "…") // Horizontal ellipsis
    .replace(/&nbsp;/g, " ")
    .replace(/&apos;/g, "'")
    .replace(/&hellip;/g, "…")
    .replace(/&ndash;/g, "–")
    .replace(/&mdash;/g, "—")
    .replace(/&rsquo;/g, "'")
    .replace(/&lsquo;/g, "'")
    .replace(/&rdquo;/g, '"')
    .replace(/&ldquo;/g, '"');

  // Remove HTML tags
  cleaned = cleaned.replace(/<[^>]*>/g, "");

  // Remove "Replies: X Rating: Y stars" patterns
  cleaned = cleaned
    .replace(/^.*?Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi, "")
    .replace(/^.*?Replies:\s*\d+\s*/gi, "")
    .replace(/^.*?Rating:\s*\d+\s*stars?\s*/gi, "")
    .replace(/Replies:\s*\d+\s*Rating:\s*\d+\s*stars?\s*/gi, "")
    .replace(/Replies:\s*\d+\s*/gi, "")
    .replace(/Rating:\s*\d+\s*stars?\s*/gi, "");

  // Clean up extra whitespace
  cleaned = cleaned.replace(/\s+/g, " ").trim();

  return cleaned;
}

// Utility function to generate a proper review ID using the recommended format
function generateReviewId(pluginSlug, author = "", datePublished = null) {
  // Clean author name (remove special characters, limit length)
  const cleanAuthor = (author || "anonymous")
    .toLowerCase()
    .replace(/[^a-z0-9]/g, "")
    .substring(0, 15);

  // Format date as YYYY-MM-DD
  const date = datePublished ? new Date(datePublished) : new Date();
  const dateStr = date.toISOString().split("T")[0];

  // Add timestamp to ensure uniqueness
  const timestamp = Date.now().toString().slice(-6);

  return `${pluginSlug}-${cleanAuthor}-${dateStr}-${timestamp}`;
}

// Legacy function for backward compatibility
function generateFallbackReviewId(pluginSlug, title = "", author = "") {
  return generateReviewId(pluginSlug, author, new Date());
}

// Utility function to validate and filter reviews data
function validateAndFilterReviews(reviewsData, pluginSlug) {
  const validReviews = {};
  let skippedCount = 0;

  for (const [reviewId, reviewData] of Object.entries(reviewsData)) {
    // Skip reviews with invalid reviewId
    if (!reviewId || typeof reviewId !== "string" || !reviewId.trim()) {
      console.log(
        `⚠️ Skipping review with invalid ID for ${pluginSlug}: ${reviewId}`
      );
      skippedCount++;
      continue;
    }

    // Skip reviews with empty content
    if (!reviewData.title && !reviewData.content) {
      console.log(
        `⚠️ Skipping review with empty content for ${pluginSlug}: ${reviewId}`
      );
      skippedCount++;
      continue;
    }

    validReviews[reviewId.trim()] = reviewData;
  }

  if (skippedCount > 0) {
    console.log(
      `⚠️ Filtered out ${skippedCount} invalid reviews for ${pluginSlug}`
    );
  }

  return validReviews;
}

// Utility function to parse reviews from RSS XML
async function parseReviewsFromRSS(rssText, pluginSlug) {
  try {
    console.log(`Parsing RSS reviews for plugin: ${pluginSlug}`);
    console.log(`RSS content length: ${rssText.length} characters`);

    // Simple RSS parsing to extract reviews
    const reviewsData = {};
    const itemRegex = /<item>(.*?)<\/item>/gs;
    let match;
    let itemCount = 0;

    while ((match = itemRegex.exec(rssText)) !== null) {
      itemCount++;
      const itemContent = match[1];
      console.log(`Processing item ${itemCount} for ${pluginSlug}`);

      // Extract review data with multiple patterns to handle different formats
      const titleMatch = itemContent.match(
        /<title>(?:<!\[CDATA\[)?(.*?)(?:\]\]>)?<\/title>/s
      );
      const linkMatch = itemContent.match(/<link>(.*?)<\/link>/);
      const guidMatch = itemContent.match(/<guid[^>]*>(.*?)<\/guid>/);
      const descMatch = itemContent.match(
        /<description>(?:<!\[CDATA\[)?(.*?)(?:\]\]>)?<\/description>/s
      );
      const dateMatch = itemContent.match(/<pubDate>(.*?)<\/pubDate>/);
      const authorMatch = itemContent.match(
        /<dc:creator>(?:<!\[CDATA\[)?(.*?)(?:\]\]>)?<\/dc:creator>/
      );

      if (titleMatch && (linkMatch || guidMatch)) {
        let reviewId = guidMatch ? guidMatch[1] : linkMatch[1]; // Use guid or link as unique ID

        // Validate and clean reviewId, generate proper reviewId if needed
        if (!reviewId || typeof reviewId !== "string" || !reviewId.trim()) {
          const author = authorMatch ? authorMatch[1].trim() : "Anonymous";
          const date = dateMatch ? new Date(dateMatch[1]) : new Date();
          reviewId = generateReviewId(pluginSlug, author, date);
          console.log(`⚠️ Generated reviewId for ${pluginSlug}: ${reviewId}`);
        }

        reviewId = reviewId.trim();
        const rawTitle = titleMatch[1].trim();
        const rawContent = descMatch ? descMatch[1].trim() : "";
        const date = dateMatch ? new Date(dateMatch[1]) : new Date();
        const author = authorMatch ? authorMatch[1].trim() : "Anonymous";

        // Clean and decode title and content
        const cleanTitle = cleanAndDecodeContent(rawTitle);
        const cleanContent = cleanAndDecodeContent(rawContent);

        // Extract rating from title - handle multiple formats
        let rating = 5; // Default rating

        // Format 1: "★★★★★ Title"
        const starMatch = rawTitle.match(/^(★+)/);
        if (starMatch) {
          rating = starMatch[1].length;
        } else {
          // Format 2: "Title (5 stars)" or "Title (1 star)"
          const ratingTextMatch = rawTitle.match(/\((\d+)\s+stars?\)/i);
          if (ratingTextMatch) {
            rating = parseInt(ratingTextMatch[1]);
          } else {
            // Format 3: Extract from description "Rating: X stars"
            const descRatingMatch = rawContent.match(
              /Rating:\s*(\d+)\s*stars?/i
            );
            if (descRatingMatch) {
              rating = parseInt(descRatingMatch[1]);
            }
          }
        }

        // Further clean title (remove rating indicators)
        const finalTitle = cleanTitle
          .replace(/^★+\s*/, "") // Remove stars
          .replace(/\s*\(\d+\s+stars?\)$/i, "") // Remove (X stars)
          .trim();

        // Skip if title or content is empty after cleaning
        if (!finalTitle && !cleanContent) {
          console.log(
            `⚠️ Skipped item ${itemCount} for ${pluginSlug}: empty title and content after cleaning`
          );
          continue;
        }

        reviewsData[reviewId] = {
          title: finalTitle || "No Title",
          content: cleanContent,
          rating: rating,
          author: cleanAndDecodeContent(author),
          date: date,
          reviewUrl: linkMatch ? linkMatch[1] : guidMatch[1],
          originalData: { rssItem: itemContent },
        };

        console.log(
          `✅ Parsed review for ${pluginSlug}: "${finalTitle}" (${rating} stars) by ${author}`
        );
      } else {
        console.log(
          `⚠️ Skipped item ${itemCount} for ${pluginSlug}: missing title or link/guid`
        );
      }
    }

    console.log(
      `✅ Successfully parsed ${
        Object.keys(reviewsData).length
      } reviews from ${itemCount} RSS items for ${pluginSlug}`
    );

    // Validate and filter reviews before returning
    return validateAndFilterReviews(reviewsData, pluginSlug);
  } catch (error) {
    console.error(`❌ Error parsing RSS reviews for ${pluginSlug}:`, error);
    return {};
  }
}

// Utility function to fetch plugin reviews from RSS feed
async function fetchPluginReviews(pluginSlug, pluginName) {
  try {
    console.log(`Fetching reviews for plugin: ${pluginSlug}`);

    // WordPress plugin reviews RSS feed URL
    const reviewsUrl = `https://wordpress.org/support/plugin/${pluginSlug}/reviews/feed/`;
    const response = await fetch(reviewsUrl);

    if (!response.ok) {
      console.log(
        `Failed to fetch reviews for ${pluginSlug}: ${response.status}`
      );
      return null;
    }

    const rssText = await response.text();

    // Simple RSS parsing to extract reviews
    const reviewsData = {};
    const itemRegex = /<item>(.*?)<\/item>/gs;
    let match;

    while ((match = itemRegex.exec(rssText)) !== null) {
      const itemContent = match[1];

      // Extract review data
      const titleMatch = itemContent.match(
        /<title><!\[CDATA\[(.*?)\]\]><\/title>/
      );
      const linkMatch = itemContent.match(/<link>(.*?)<\/link>/);
      const descMatch = itemContent.match(
        /<description><!\[CDATA\[(.*?)\]\]><\/description>/
      );
      const dateMatch = itemContent.match(/<pubDate>(.*?)<\/pubDate>/);
      const authorMatch = itemContent.match(
        /<dc:creator><!\[CDATA\[(.*?)\]\]><\/dc:creator>/
      );

      if (titleMatch && linkMatch) {
        let reviewId = linkMatch[1]; // Use link as unique ID

        // Validate and clean reviewId, generate proper reviewId if needed
        if (!reviewId || typeof reviewId !== "string" || !reviewId.trim()) {
          const author = authorMatch ? authorMatch[1] : "Anonymous";
          const date = dateMatch ? new Date(dateMatch[1]) : new Date();
          reviewId = generateReviewId(pluginSlug, author, date);
          console.log(`⚠️ Generated reviewId for ${pluginSlug}: ${reviewId}`);
        }

        reviewId = reviewId.trim();
        const title = titleMatch[1];
        const content = descMatch ? descMatch[1] : "";
        const date = dateMatch ? new Date(dateMatch[1]) : new Date();
        const author = authorMatch ? authorMatch[1] : "Anonymous";

        // Extract rating from title (usually in format "★★★★★ Title")
        const ratingMatch = title.match(/^(★+)/);
        const rating = ratingMatch ? ratingMatch[1].length : 5; // Default to 5 if no stars found

        reviewsData[reviewId] = {
          title: title.replace(/^★+\s*/, ""), // Remove stars from title
          content,
          rating,
          author,
          date,
          reviewUrl: linkMatch[1],
          originalData: { rssItem: itemContent },
        };
      }
    }

    // Validate and filter reviews before storing
    const validReviewsData = validateAndFilterReviews(reviewsData, pluginSlug);

    // Convert object to array format for new schema
    const reviewsArray = Object.keys(validReviewsData).map((reviewId) => ({
      reviewId,
      ...validReviewsData[reviewId],
    }));

    // Store reviews using upsert
    const result = await PluginReview.upsertPluginReviews(
      pluginSlug,
      pluginName,
      reviewsArray
    );

    console.log(
      `Successfully stored ${reviewsArray.length} reviews for ${pluginSlug}`
    );
    return result;
  } catch (error) {
    console.error(`Error fetching reviews for ${pluginSlug}:`, error);
    return null;
  }
}

// Utility function to fetch plugin versions
async function fetchPluginVersions(pluginSlug, pluginName, pluginData = null) {
  try {
    console.log(`Fetching versions for plugin: ${pluginSlug}`);

    let versionsData = {};

    // If plugin data is provided, use versions from it
    if (
      pluginData &&
      pluginData.versions &&
      typeof pluginData.versions === "object"
    ) {
      versionsData = pluginData.versions;
    } else {
      // Otherwise, fetch from WordPress API
      const apiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${pluginSlug}&request[fields][icons]=true`;
      const response = await fetch(apiUrl);

      if (!response.ok) {
        console.log(
          `Failed to fetch plugin info for ${pluginSlug}: ${response.status}`
        );
        return null;
      }

      const pluginInfo = await response.json();

      if (
        pluginInfo &&
        pluginInfo.versions &&
        typeof pluginInfo.versions === "object"
      ) {
        versionsData = pluginInfo.versions;
      }
    }

    if (Object.keys(versionsData).length === 0) {
      console.log(`No versions data found for ${pluginSlug}`);
      return null;
    }

    // Store versions using the existing method
    const result = await PluginVersion.storePluginVersions(
      pluginSlug,
      pluginName,
      versionsData
    );

    console.log(
      `Successfully stored ${
        Object.keys(versionsData).length
      } versions for ${pluginSlug}`
    );
    return result;
  } catch (error) {
    console.error(`Error fetching versions for ${pluginSlug}:`, error);
    return null;
  }
}

// Fetch plugins from WordPress API and store in database
router.post("/fetch", authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { page = 1, per_page = 100 } = req.body;

    console.log(`Fetching plugins: page ${page}, per_page ${per_page}`);

    // Fetch from WordPress API
    const apiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[page]=${page}&request[per_page]=${per_page}`;

    const response = await fetch(apiUrl);
    if (!response.ok) {
      throw new Error(`WordPress API error: ${response.status}`);
    }

    const data = await response.json();

    if (!data.plugins || !Array.isArray(data.plugins)) {
      throw new Error("Invalid response format from WordPress API");
    }

    const plugins = data.plugins;
    const batchId = `batch_${Date.now()}_${page}`;

    console.log(`Received ${plugins.length} plugins from WordPress API`);

    // Format plugins data as specified
    const allPluginsFormatted = {};
    const pluginRankData = [];

    for (let index = 0; index < plugins.length; index++) {
      const plugin = plugins[index];
      const slug = plugin.slug;
      const rank = (page - 1) * per_page + index + 1; // Calculate global rank
      const title = plugin.name;

      // Format as specified in requirements
      allPluginsFormatted[slug] = {
        plugin_data: plugin,
        rank: rank,
        title: title,
      };

      // Store rank data for history (only if rank is valid)
      if (rank > 0) {
        pluginRankData.push({
          slug: slug,
          rank: rank,
          fetchDate: new Date(),
          batchId: batchId,
        });
      }

      // Store or update plugin in database
      try {
        // Only save if rank is valid
        if (rank > 0) {
          await Plugin.findOneAndUpdate(
            { slug: slug },
            {
              slug: slug,
              name: plugin.name,
              currentRank: rank,
              short_description: plugin.short_description || "",
              icons: plugin.icons || {},
              lastFetched: new Date(),
              $inc: { fetchCount: 1 },
            },
            {
              upsert: true,
              new: true,
              setDefaultsOnInsert: true,
            }
          );
        } else {
          console.warn(`Skipping plugin ${slug} with invalid rank: ${rank}`);
        }
      } catch (dbError) {
        console.error(`Error saving plugin ${slug}:`, dbError);
      }
    }

    // Skip bulk rank history insertion during general plugin fetching
    // Rank history will only be stored for plugins added to addedplugins collection
    console.log(
      `Skipped rank history insertion for ${pluginRankData.length} plugins during bulk fetch`
    );

    // Clear the rank data array to prevent memory issues
    pluginRankData.length = 0;

    res.json({
      success: true,
      message: `Successfully fetched and stored ${plugins.length} plugins`,
      data: {
        page: parseInt(page),
        per_page: parseInt(per_page),
        plugins_count: plugins.length,
        total_plugins: data.info?.results || "unknown",
        batch_id: batchId,
        formatted_data: allPluginsFormatted,
      },
    });
  } catch (error) {
    console.error("Fetch plugins error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to fetch plugins",
      error: error.message,
    });
  }
});

// Helper function to safely upsert plugin data (optimized for 5 fields: slug, name, currentRank, short_description, icons)
const upsertPluginData = async (pluginData, rank) => {
  try {
    const slug = pluginData.slug;
    if (!slug) {
      throw new Error("Plugin slug is required");
    }

    // Prepare the update data with the 5 required fields
    const updateData = {
      slug: slug,
      name: pluginData.name || slug,
      currentRank: rank > 0 ? rank : null,
      short_description: pluginData.short_description || "",
      icons: pluginData.icons || {},
      lastFetched: new Date(),
      $inc: { fetchCount: 1 },
    };

    const result = await Plugin.findOneAndUpdate({ slug: slug }, updateData, {
      upsert: true,
      new: true,
      setDefaultsOnInsert: true,
      runValidators: true,
    });

    return { success: true, plugin: result };
  } catch (error) {
    console.error(`Error upserting plugin ${pluginData.slug}:`, error);
    return { success: false, error: error.message };
  }
};

// Helper function to fetch a page with enhanced retry logic and timeout handling
const fetchPageWithRetry = async (page, perPage, maxRetries = 3) => {
  const url = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[page]=${page}&request[per_page]=${perPage}`;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const startTime = Date.now();

    try {
      const controller = new AbortController();
      const timeoutDuration = 15000 + attempt * 5000; // Increase timeout with each retry
      const timeoutId = setTimeout(() => controller.abort(), timeoutDuration);

      logger.info(`Fetching page ${page}`, {
        attempt,
        maxRetries,
        timeoutDuration,
        url,
      });

      const response = await fetch(url, {
        signal: controller.signal,
        headers: {
          "User-Agent": "PluginSight-Fetcher/1.0",
          Accept: "application/json",
        },
      });

      clearTimeout(timeoutId);
      const fetchDuration = Date.now() - startTime;

      // Check for rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get("retry-after") || 60;
        logger.logRateLimit({
          page,
          retryAfter,
          attempt,
          message: "Rate limited by WordPress API",
        });

        if (attempt < maxRetries) {
          await new Promise((resolve) =>
            setTimeout(resolve, retryAfter * 1000)
          );
          continue;
        }
      }

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      // Validate response structure
      if (!data || typeof data !== "object") {
        throw new Error("Invalid response format");
      }

      // Validate plugins array
      if (!data.plugins || !Array.isArray(data.plugins)) {
        throw new Error("Invalid plugins data in response");
      }

      logger.success(`Successfully fetched page ${page}`, {
        pluginCount: data.plugins.length,
        fetchDuration,
        attempt,
      });

      return { page, data, success: true, fetchDuration };
    } catch (error) {
      const isLastAttempt = attempt === maxRetries;
      const isTimeoutError = error.name === "AbortError";
      const isNetworkError =
        error.message.includes("fetch") || error.code === "ECONNRESET";
      const fetchDuration = Date.now() - startTime;

      logger.logNetworkError(url, attempt, maxRetries, error);

      if (isLastAttempt) {
        logger.error(
          `Failed to fetch page ${page} after ${maxRetries} attempts`,
          error,
          {
            page,
            maxRetries,
            totalDuration: fetchDuration,
          }
        );
        return { page, error: error.message, success: false };
      } else {
        const waitTime = Math.min(1000 * Math.pow(2, attempt), 10000); // Exponential backoff, max 10s

        logger.warn(`Retrying page ${page}`, {
          attempt,
          maxRetries,
          error: error.message,
          waitTime,
          isTimeoutError,
          isNetworkError,
        });

        // Add extra delay for timeout or network errors
        if (isTimeoutError || isNetworkError) {
          await new Promise((resolve) => setTimeout(resolve, waitTime + 2000));
        } else {
          await new Promise((resolve) => setTimeout(resolve, waitTime));
        }
      }
    }
  }
};

// Fetch plugins from WordPress API in smaller batches (optimized for serverless)
router.post(
  "/fetch-bulk",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      console.log("Starting optimized plugin fetch...");
      console.log("Request body:", req.body);
      console.log("User:", req.user);

      // Validate database connection
      if (!Plugin) {
        throw new Error("Plugin model not available");
      }

      // Handle cases where req.body might be undefined or missing maxPages
      const requestBody = req.body || {};
      const { maxPages = null, fetchAll = false } = requestBody;
      const perPage = 100;

      let totalPages = maxPages || 10; // Default to 10 pages if not specified

      // If fetchAll is true, get the actual total from WordPress API
      if (fetchAll) {
        console.log("Fetching total plugin count from WordPress API...");
        const countUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[per_page]=1`;
        const countResponse = await fetch(countUrl);

        if (!countResponse.ok) {
          throw new Error(`WordPress API error: ${countResponse.status}`);
        }

        const countData = await countResponse.json();
        const totalPlugins = countData.info?.results || 0;
        totalPages = Math.ceil(totalPlugins / perPage);

        console.log(
          `Total plugins available: ${totalPlugins}, Total pages: ${totalPages}`
        );
      }

      console.log(
        `Fetching up to ${totalPages} pages (${totalPages * perPage} plugins)`
      );

      let successfulPages = 0;
      let failedPages = 0;
      let totalProcessedPlugins = 0;
      const errors = [];

      // Process pages in smaller batches to balance speed and reliability
      const batchSize = fetchAll ? 3 : 5; // Smaller batches for full fetch to be more reliable

      for (
        let batchStart = 1;
        batchStart <= totalPages;
        batchStart += batchSize
      ) {
        const batchEnd = Math.min(batchStart + batchSize - 1, totalPages);
        console.log(
          `Processing batch: pages ${batchStart}-${batchEnd} of ${totalPages}`
        );

        // Process pages in parallel within each batch
        const batchPromises = [];
        for (let page = batchStart; page <= batchEnd; page++) {
          batchPromises.push(fetchPageWithRetry(page, perPage, 3));
        }

        // Wait for current batch to complete
        const batchResults = await Promise.all(batchPromises);

        // Process results sequentially to avoid database overload
        for (const result of batchResults) {
          try {
            if (result.success) {
              const plugins = result.data.plugins || [];
              console.log(
                `Page ${result.page}: Processing ${plugins.length} plugins`
              );

              // Initialize arrays for the entire page
              const allPluginsToUpsert = [];
              const allPluginRankData = [];

              // Process plugins in smaller batches for database operations
              const dbBatchSize = 25;
              for (let i = 0; i < plugins.length; i += dbBatchSize) {
                const batch = plugins.slice(i, i + dbBatchSize);
                const batchId = `batch_${Date.now()}_${result.page}_${i}`;

                for (let index = 0; index < batch.length; index++) {
                  const plugin = batch[index];
                  const slug = plugin.slug;
                  const globalIndex = i + index;
                  const rank = (result.page - 1) * perPage + globalIndex + 1;

                  if (rank > 0 && slug) {
                    allPluginsToUpsert.push({
                      updateOne: {
                        filter: { slug: slug },
                        update: {
                          $set: {
                            slug: slug,
                            name: plugin.name,
                            currentRank: rank,
                            short_description: plugin.short_description || "",
                            lastFetched: new Date(),
                          },
                          $inc: { fetchCount: 1 },
                          $setOnInsert: {
                            isActive: true,
                            createdAt: new Date(),
                          },
                        },
                        upsert: true,
                      },
                    });

                    // Add to rank history
                    allPluginRankData.push({
                      slug: slug,
                      rank: rank,
                      fetchDate: new Date(),
                      batchId: batchId,
                    });
                  }
                }
              }

              // Bulk upsert to database
              if (allPluginsToUpsert.length > 0) {
                try {
                  console.log(
                    `Attempting to save ${allPluginsToUpsert.length} plugins to database...`
                  );

                  const dbResult = await Plugin.bulkWrite(allPluginsToUpsert, {
                    ordered: false,
                  });
                  console.log(
                    `✅ Saved batch of ${allPluginsToUpsert.length} plugins to database`
                  );
                  console.log(`Bulk write result:`, {
                    insertedCount: dbResult.insertedCount,
                    modifiedCount: dbResult.modifiedCount,
                    upsertedCount: dbResult.upsertedCount,
                  });
                } catch (dbError) {
                  console.error(
                    `❌ Database error for batch:`,
                    dbError.message
                  );
                  errors.push(`Database error: ${dbError.message}`);
                }
              }

              // Save rank history
              if (allPluginRankData.length > 0) {
                try {
                  await PluginRankHistory.insertMany(allPluginRankData, {
                    ordered: false,
                  });
                  console.log(
                    `✅ Saved ${allPluginRankData.length} rank history entries`
                  );
                } catch (historyError) {
                  console.error(
                    "❌ Error saving rank history:",
                    historyError.message
                  );
                  // Don't add to errors array as this is not critical
                }
              }

              successfulPages++;
              totalProcessedPlugins += plugins.length;
            } else {
              failedPages++;
              errors.push(`Page ${result.page}: ${result.error}`);
              console.error(
                `❌ Failed to fetch page ${result.page}:`,
                result.error
              );
            }
          } catch (pageError) {
            failedPages++;
            errors.push(`Page processing error: ${pageError.message}`);
            console.error(`❌ Error processing page:`, pageError.message);
          }
        }

        // Add delay between batches to be respectful to APIs and database
        if (batchEnd < totalPages) {
          const delayTime = fetchAll ? 2000 : 1000; // Longer delay for full fetch
          console.log(`⏳ Waiting ${delayTime}ms before next batch...`);
          await new Promise((resolve) => setTimeout(resolve, delayTime));
        }
      }

      // Return summary
      res.json({
        success: true,
        message: `Plugin fetch completed. Processed ${totalProcessedPlugins} plugins from ${successfulPages} pages.`,
        summary: {
          totalProcessedPlugins,
          successfulPages,
          failedPages,
          totalPagesRequested: totalPages,
          fetchAll: fetchAll,
          errors: errors.slice(0, 10), // Limit error details
        },
      });
    } catch (error) {
      console.error("Bulk fetch error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to fetch plugins",
        error: error.message,
      });
    }
  }
);

// Check if plugins exist in database (for button text logic)
router.get(
  "/check-database",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const pluginCount = await Plugin.countDocuments();

      res.json({
        success: true,
        hasPlugins: pluginCount > 0,
        pluginCount: pluginCount,
        message:
          pluginCount > 0
            ? "Plugins exist in database"
            : "No plugins in database",
      });
    } catch (error) {
      console.error("Database check error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to check database",
        error: error.message,
      });
    }
  }
);

// Test endpoint to verify database collections (no auth for testing)
router.get("/test-db", async (req, res) => {
  try {
    console.log("Testing database collections...");

    // Test creating a simple plugin entry to ensure collections exist
    const testPlugin = new Plugin({
      slug: "test-plugin-" + Date.now(),
      name: "Test Plugin",
      currentRank: 999999,
      short_description: "Test plugin description",
      lastFetched: new Date(),
      fetchCount: 1,
    });

    await testPlugin.save();
    console.log("✅ Test plugin created successfully");

    // Clean up test plugin
    await Plugin.deleteOne({ slug: testPlugin.slug });
    console.log("✅ Test plugin cleaned up");

    res.json({
      success: true,
      message: "Database collections are working correctly",
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Database test error:", error);
    res.status(500).json({
      success: false,
      message: "Database test failed",
      error: error.message,
    });
  }
});

// Fetch ALL plugins from WordPress API in batches
router.post("/fetch-all", authenticateToken, requireAdmin, async (req, res) => {
  const fetchStartTime = Date.now();
  const sessionId = `fetch_${Date.now()}_${Math.random()
    .toString(36)
    .substr(2, 9)}`;

  try {
    logger.info("Starting fetch-all operation", {
      sessionId,
      userId: req.user._id,
      userRole: req.user.role,
    });

    // Set up streaming response
    res.writeHead(200, {
      "Content-Type": "application/json",
      "Transfer-Encoding": "chunked",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    });

    // Helper function to send progress updates
    const sendProgress = (data) => {
      res.write(JSON.stringify({ type: "progress", ...data }) + "\n");
      logger.progress("Fetch progress update", { sessionId, ...data });
    };

    // First, get the total count of plugins
    const countUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[per_page]=1`;
    logger.info("Fetching total plugin count", { sessionId, countUrl });

    const countResponse = await fetch(countUrl);

    if (!countResponse.ok) {
      throw new Error(`WordPress API error: ${countResponse.status}`);
    }

    const countData = await countResponse.json();
    const totalPlugins = countData.info?.results || 0;
    const perPage = 100;
    // Use the actual pages from API response, not calculated
    const totalPages =
      countData.info?.pages || Math.ceil(totalPlugins / perPage);

    logger.info("Total plugin count retrieved", {
      sessionId,
      totalPlugins,
      totalPages,
      apiPages: countData.info?.pages,
    });

    // Send initial progress
    const startTime = Date.now();
    sendProgress({
      current: 0,
      total: totalPlugins,
      page: 0,
      totalPages: totalPages,
      successCount: 0,
      errorCount: 0,
      startTime: startTime,
      estimatedTimeRemaining: null,
      averageTimePerPage: null,
      message: `Starting to fetch ${totalPlugins.toLocaleString()} plugins from ${totalPages} pages...`,
    });

    let successfulPages = 0;
    let failedPages = 0;
    let totalProcessedPlugins = 0;
    const errors = [];
    const pageTimings = [];

    // Process pages in batches to avoid overwhelming the API
    const batchSize = 5; // Process 5 pages at a time
    for (
      let batchStart = 1;
      batchStart <= totalPages;
      batchStart += batchSize
    ) {
      const batchEnd = Math.min(batchStart + batchSize - 1, totalPages);
      const batchPromises = [];

      for (let page = batchStart; page <= batchEnd; page++) {
        batchPromises.push(
          fetchPageWithRetry(page, perPage, 3) // 3 retry attempts
        );
      }

      // Wait for current batch to complete
      const batchResults = await Promise.all(batchPromises);

      // Process successful results
      for (const result of batchResults) {
        const pageStartTime = Date.now();

        if (result.success) {
          try {
            const plugins = result.data.plugins || [];
            const batchId = `batch_${Date.now()}_${result.page}`;
            const pluginRankData = [];

            for (let index = 0; index < plugins.length; index++) {
              const plugin = plugins[index];
              const slug = plugin.slug;
              const rank = (result.page - 1) * perPage + index + 1;

              // Store rank data for history (only if rank is valid)
              if (rank > 0) {
                pluginRankData.push({
                  slug: slug,
                  rank: rank,
                  fetchDate: new Date(),
                  batchId: batchId,
                });
              }

              // Store or update plugin in database using enhanced upsert
              const upsertResult = await upsertPluginData(plugin, rank);
              if (!upsertResult.success) {
                console.error(
                  `Failed to upsert plugin ${slug}:`,
                  upsertResult.error
                );
              }
            }

            // Skip bulk rank history insertion during general plugin fetching
            // Rank history will only be stored for plugins added to addedplugins collection
            console.log(
              `Skipped rank history insertion for ${pluginRankData.length} plugins during bulk fetch (page ${result.page})`
            );

            // Clear the rank data array to prevent memory issues
            pluginRankData.length = 0;

            successfulPages++;
            totalProcessedPlugins += plugins.length;

            // Track page timing for estimation
            const pageEndTime = Date.now();
            const pageProcessingTime = pageEndTime - pageStartTime;
            pageTimings.push(pageProcessingTime);

            // Calculate average time per page and estimate remaining time
            const averageTimePerPage =
              pageTimings.reduce((a, b) => a + b, 0) / pageTimings.length;
            const remainingPages = totalPages - result.page;
            const estimatedTimeRemaining = remainingPages * averageTimePerPage;

            console.log(
              `✅ Page ${result.page}/${totalPages} completed (${plugins.length} plugins) in ${pageProcessingTime}ms`
            );

            // Send enhanced progress update
            sendProgress({
              current: totalProcessedPlugins,
              total: totalPlugins,
              page: result.page,
              totalPages: totalPages,
              successCount: successfulPages,
              errorCount: failedPages,
              startTime: startTime,
              averageTimePerPage: Math.round(averageTimePerPage),
              estimatedTimeRemaining: Math.round(estimatedTimeRemaining),
              percentComplete: Math.round((result.page / totalPages) * 100),
              pluginsPerSecond: Math.round(
                totalProcessedPlugins / ((Date.now() - startTime) / 1000)
              ),
              message: `Processing page ${
                result.page
              }/${totalPages} - ${totalProcessedPlugins.toLocaleString()}/${totalPlugins.toLocaleString()} plugins processed (${Math.round(
                (result.page / totalPages) * 100
              )}% complete)`,
            });
          } catch (processError) {
            console.error(
              `Error processing page ${result.page}:`,
              processError
            );
            failedPages++;
            errors.push(`Page ${result.page}: ${processError.message}`);
          }
        } else {
          failedPages++;
          console.warn(
            `⚠️ Skipping failed page ${result.page}: ${result.error}`
          );
          errors.push(`Page ${result.page}: ${result.error}`);
        }
      }

      // Add a small delay between batches to be respectful to the API
      if (batchEnd < totalPages) {
        await new Promise((resolve) => setTimeout(resolve, 1000));
      }
    }

    // Calculate final statistics
    const endTime = Date.now();
    const totalDuration = endTime - startTime;
    const totalSessionDuration = endTime - fetchStartTime;
    const averagePluginsPerSecond = Math.round(
      totalProcessedPlugins / (totalDuration / 1000)
    );
    const averageTimePerPage =
      pageTimings.length > 0
        ? Math.round(
            pageTimings.reduce((a, b) => a + b, 0) / pageTimings.length
          )
        : 0;

    const finalStats = {
      sessionId,
      totalProcessedPlugins,
      successfulPages,
      failedPages,
      totalPages,
      totalPlugins,
      totalDuration,
      totalSessionDuration,
      averageTimePerPage,
      averagePluginsPerSecond,
      successRate: Math.round((successfulPages / totalPages) * 100),
      errorCount: errors.length,
      userId: req.user._id,
    };

    // Log final statistics
    logger.logFetchStats(finalStats);
    logger.success("Fetch-all operation completed", finalStats);

    // Send completion message with detailed statistics
    res.write(
      JSON.stringify({
        type: "complete",
        success: true,
        message: `Batch fetch completed! ${successfulPages} pages successful, ${failedPages} pages failed.`,
        summary: finalStats,
        errors: errors.slice(0, 10), // Limit error list
      }) + "\n"
    );

    res.end();
  } catch (error) {
    const errorSessionDuration = Date.now() - fetchStartTime;

    logger.error("Fetch-all operation failed", error, {
      sessionId,
      errorSessionDuration,
      userId: req.user?._id,
      userRole: req.user?.role,
    });

    try {
      res.write(
        JSON.stringify({
          type: "error",
          success: false,
          message: "Failed to fetch all plugins",
          error: error.message,
          sessionId,
          duration: errorSessionDuration,
        }) + "\n"
      );
      res.end();
    } catch (writeError) {
      logger.error("Error writing error response to stream", writeError, {
        sessionId,
        originalError: error.message,
      });

      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: "Failed to fetch all plugins",
          error: error.message,
          sessionId,
        });
      }
    }
  }
});

// Get all plugins with pagination
router.get("/", authenticateToken, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      search = "",
      sortBy = "currentRank",
      sortOrder = "asc",
    } = req.query;

    const skip = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    let query = {};
    if (search) {
      const regex = new RegExp(search, "i");
      query = {
        $or: [{ name: regex }, { slug: regex }, { short_description: regex }],
      };
    }

    // Build sort
    const sort = {};
    sort[sortBy] = sortOrder === "desc" ? -1 : 1;

    const plugins = await Plugin.find(query)
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    const totalCount = await Plugin.countDocuments(query);

    res.json({
      success: true,
      plugins,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / parseInt(limit)),
      },
    });
  } catch (error) {
    console.error("Get plugins error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get plugins",
      error: error.message,
    });
  }
});

// Get plugin slugs only for refresh all functionality
router.get("/added/slugs", authenticateToken, async (req, res) => {
  try {
    console.log("Get plugin slugs for refresh all functionality");

    // Get all active added plugins from admin/superadmin users
    const User = (await import("../models/User.js")).default;
    const adminUsers = await User.find({
      role: { $in: ["admin", "superadmin"] },
      isActive: true,
    }).select("_id");

    const adminUserIds = adminUsers.map((user) => user._id);

    const addedPlugins = await AddedPlugin.find({
      userId: { $in: adminUserIds },
      isActive: true,
    }).select("pluginSlug");

    const slugs = addedPlugins.map((plugin) => plugin.pluginSlug);

    res.json({
      success: true,
      slugs: slugs,
      count: slugs.length,
    });
  } catch (error) {
    console.error("Get plugin slugs error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get plugin slugs",
      error: error.message,
    });
  }
});

// Get all added plugins (visible to all users - no userID filtering)
router.get("/added", authenticateToken, async (req, res) => {
  try {
    const {
      sortBy = "addedAt",
      sortOrder = "desc",
      limit = 20,
      page = 1,
    } = req.query;

    console.log(
      "Get ALL added plugins from addedplugins collection (no userID filtering)"
    );

    const limitNum = parseInt(limit);
    const pageNum = parseInt(page);
    const skip = (pageNum - 1) * limitNum;

    // Get ALL plugins from addedplugins collection - no userID filtering
    const addedPlugins = await AddedPlugin.find({
      isActive: true,
    })
      .sort({ [sortBy]: sortOrder === "desc" ? -1 : 1 })
      .skip(skip)
      .limit(limitNum);

    // Enhance plugins with data from plugininformations and plugindownloaddatas collections
    const enhancedPlugins = await Promise.all(
      addedPlugins.map(async (plugin) => {
        const pluginSlug = plugin.pluginSlug;
        try {
          // Fetch from plugininformations and plugindownloaddatas collections
          const [downloadData, pluginInfo] = await Promise.all([
            PluginDownloadData.getPluginDownloadData(pluginSlug),
            PluginInformation.getPluginInformation(pluginSlug),
          ]);

          // Download data history (last 30 days)
          let downloadDataHistory = [];
          let downloadTrend = null;

          if (downloadData && downloadData.downloadData) {
            const downloadEntries = Object.entries(downloadData.downloadData)
              .filter(
                ([date, downloads]) =>
                  date !== "all_time" &&
                  !isNaN(Date.parse(date)) &&
                  typeof downloads === "number"
              )
              .sort(([dateA], [dateB]) => new Date(dateA) - new Date(dateB));
            const last30 = downloadEntries.slice(-30);
            downloadDataHistory = last30.map(([date, downloads]) => ({
              date,
              downloads,
            }));

            // Calculate download trends (yesterday vs day before yesterday)
            const today = new Date();
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            const dayBefore = new Date(today);
            dayBefore.setDate(dayBefore.getDate() - 2);

            const yesterdayStr = yesterday.toISOString().split("T")[0];
            const dayBeforeStr = dayBefore.toISOString().split("T")[0];

            const yesterdayDownloads =
              downloadData.downloadData[yesterdayStr] || 0;
            const dayBeforeDownloads =
              downloadData.downloadData[dayBeforeStr] || 0;

            if (yesterdayDownloads >= 0 && dayBeforeDownloads >= 0) {
              const change = yesterdayDownloads - dayBeforeDownloads;
              const changePercent =
                dayBeforeDownloads > 0
                  ? Math.round((Math.abs(change) / dayBeforeDownloads) * 100)
                  : 0;
              downloadTrend = {
                yesterdayDownloads,
                dayBeforeDownloads,
                change,
                changePercent,
                isPositive: change >= 0,
              };
            }
          }

          // Plugin version from plugininformations collection's version field
          let pluginVersion =
            pluginInfo?.version || plugin.pluginData?.version || null;

          // Last release date from plugininformations collection's pluginInfo field's last_updated field
          let lastReleaseDate =
            pluginInfo?.pluginInfo?.last_updated ||
            pluginInfo?.lastUpdated ||
            plugin.pluginData?.last_updated ||
            null;

          // numRatings from plugininformations collection's pluginInfo field's rating field
          let pluginRating =
            pluginInfo?.pluginInfo?.rating || pluginInfo?.rating || null;
          let numRatings =
            pluginInfo?.pluginInfo?.num_ratings || pluginInfo?.numRatings || 0;

          // Plugin versions (current and previous) from plugininformations collection
          let currentVersion = pluginInfo?.version || null;
          let previousVersions = [];
          const versionsData =
            pluginInfo?.pluginInfo?.versions || pluginInfo?.versions;
          if (versionsData && typeof versionsData === "object") {
            const versionKeys = Object.keys(versionsData);
            // Sort versions and get the previous ones (excluding current)
            const sortedVersions = versionKeys
              .filter((v) => v !== currentVersion)
              .sort((a, b) => {
                // Simple version comparison - you might want to use a proper semver library
                const aParts = a.split(".").map(Number);
                const bParts = b.split(".").map(Number);
                for (
                  let i = 0;
                  i < Math.max(aParts.length, bParts.length);
                  i++
                ) {
                  const aPart = aParts[i] || 0;
                  const bPart = bParts[i] || 0;
                  if (aPart !== bPart) return bPart - aPart; // Descending order
                }
                return 0;
              });
            previousVersions = sortedVersions.slice(0, 5); // Get top 5 previous versions
          }

          return {
            ...plugin.toObject(),
            version: pluginVersion,
            lastReleaseDate: lastReleaseDate,
            icons: plugin.icons || {},
            downloadDataHistory,
            downloadTrend,
            // Plugin ratings
            rating: pluginRating,
            numRatings: numRatings,
            // Plugin versions
            currentVersion,
            previousVersions,
            // Rank history from addedplugins collection
            rankHistory: plugin.rankHistory || [],
            // pluginInformation from plugininformations collection's pluginInfo field
            pluginInformation: pluginInfo?.pluginInfo || null,
          };
        } catch (error) {
          console.error(`Error enhancing plugin ${pluginSlug}:`, error);
          return {
            ...plugin.toObject(),
            version: plugin.pluginData?.version || null,
            lastReleaseDate: plugin.pluginData?.last_updated || null,
            icons: plugin.icons || {},
            downloadDataHistory: [],
            downloadTrend: null,
            rating: null,
            numRatings: 0,
            currentVersion: null,
            previousVersions: [],
            pluginInformation: null,
          };
        }
      })
    );

    // Get total count for pagination info - no userID filtering
    const totalCount = await AddedPlugin.countDocuments({
      isActive: true,
    });

    res.json({
      success: true,
      addedPlugins: enhancedPlugins,
      count: enhancedPlugins.length,
      totalCount,
      page: pageNum,
      totalPages: Math.ceil(totalCount / limitNum),
      hasMore: skip + enhancedPlugins.length < totalCount,
      metadata: {
        requestedBy: req.user.email,
        userRole: req.user.role,
        accessibleToAllUsers: true,
        message:
          "ALL plugins from addedplugins collection - accessible to all authenticated users (no userID filtering)",
      },
    });
  } catch (error) {
    console.error("Get added plugins error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get added plugins",
      error: error.message,
    });
  }
});

// Add plugin with data from cookies (new 2-step workflow)
router.post(
  "/added-with-data",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { slug, pluginData } = req.body;

      console.log("Add plugin with data - userId:", userId, "slug:", slug);

      if (!slug) {
        return res.status(400).json({
          success: false,
          message: "Plugin slug is required",
        });
      }

      if (!pluginData) {
        return res.status(400).json({
          success: false,
          message: "Plugin data is required",
        });
      }

      console.log(
        `🚀 Starting 2-step plugin addition workflow for plugin: ${slug}`
      );

      // STEP 1: Get current rank from plugins collection
      console.log(
        `📝 Step 1: Get current rank from plugins collection for ${slug}`
      );

      const displayName = pluginData.name.split(/[-–:]|&#8211;/)[0].trim();

      // Get plugin data from existing plugins collection to get current rank and icons
      let plugin = await Plugin.findBySlug(slug);
      let currentRank = null;
      let pluginIcons = {};

      if (plugin) {
        if (plugin.currentRank && plugin.currentRank > 0) {
          currentRank = plugin.currentRank;
          console.log(
            `Found plugin ${slug} in plugins collection with rank: ${currentRank}`
          );
        }

        // Get icons from plugins collection
        if (plugin.icons && (plugin.icons["1x"] || plugin.icons["2x"])) {
          pluginIcons = plugin.icons;
          console.log(
            `Found icons for plugin ${slug} in plugins collection:`,
            pluginIcons
          );
        }
      } else {
        console.log(
          `Plugin ${slug} not found in plugins collection or no rank available`
        );

        // If plugin not found in database, try to fetch it from WordPress API to get rank
        try {
          console.log(
            `Fetching plugin ${slug} from WordPress API to get rank...`
          );

          // Search for plugin in WordPress API to get its rank
          let rank = -1;
          let found = false;
          let currentPage = 1;
          const maxPagesToSearch = 100; // Search up to 10,000 plugins

          while (!found && currentPage <= maxPagesToSearch) {
            const searchUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[page]=${currentPage}&request[per_page]=100`;
            const searchResponse = await fetch(searchUrl);

            if (searchResponse.ok) {
              const searchData = await searchResponse.json();
              if (searchData.plugins && searchData.plugins.length > 0) {
                const pluginIndex = searchData.plugins.findIndex(
                  (p) => p.slug === slug
                );
                if (pluginIndex !== -1) {
                  rank = (currentPage - 1) * 100 + pluginIndex + 1;
                  found = true;
                  console.log(
                    `Found plugin ${slug} at rank: ${rank} (page ${currentPage}, position ${
                      pluginIndex + 1
                    })`
                  );
                  break;
                }
              }
            }
            currentPage++;
          }

          if (found && rank > 0) {
            currentRank = rank;
            console.log(`Plugin ${slug} found at rank: ${currentRank}`);
          } else {
            console.log(`Plugin ${slug} not found in search results`);
            // Don't set rank to -1, leave it as null if not found
          }
        } catch (error) {
          console.error(`Error searching for plugin ${slug}:`, error);
        }
      }

      console.log(
        `✅ Step 1 Complete: Current rank determined for ${slug}: ${currentRank}`
      );

      // STEP 2: Store plugin information in plugininformations collection with rank data
      console.log(
        `📝 Step 2: Store plugin information in plugininformations collection for ${slug}`
      );

      // Store detailed plugin information in plugininformations collection with rank data and icons
      const pluginInformation = await PluginInformation.upsertPluginInformation(
        slug,
        displayName,
        pluginData,
        currentRank,
        pluginIcons
      );

      if (!pluginInformation) {
        return res.status(500).json({
          success: false,
          message: "Failed to store plugin in plugininformations collection",
        });
      }

      console.log(
        `✅ Step 2 Complete: Plugin "${displayName}" stored in plugininformations collection with rank: ${currentRank} and rankHistory initialized`
      );

      // STEP 3: Store plugin in addedplugins collection for dashboard display
      console.log(
        `📝 Step 3: Store plugin in addedplugins collection for dashboard display for ${slug}`
      );

      // Store plugin in addedplugins collection with rank data and icons
      const addedPlugin = await AddedPlugin.addPluginForUser(userId, {
        slug: slug,
        name: pluginData.name,
        displayName,
        currentRank: currentRank,
        rankGrowth: null, // Will be calculated later when we have more history
        short_description: pluginData.short_description,
        icons: pluginIcons, // Icons from plugins collection
      });

      if (!addedPlugin) {
        return res.status(500).json({
          success: false,
          message: "Failed to store plugin in addedplugins collection",
        });
      }

      console.log(
        `✅ Step 3 Complete: Plugin "${displayName}" stored in addedplugins collection with rank: ${currentRank} and rankHistory initialized`
      );

      // STEP 4: Fetch and store download data
      console.log(`📝 Step 4: Fetching and storing download data for ${slug}`);

      try {
        const downloadUrl = `https://api.wordpress.org/stats/plugin/1.0/downloads.php?slug=${slug}`;
        const downloadResponse = await fetch(downloadUrl);

        if (downloadResponse.ok) {
          const downloadData = await downloadResponse.json();

          // Store download data in plugindownloaddatas collection
          await PluginDownloadData.upsertDownloadData(
            slug,
            displayName,
            downloadData
          );

          console.log(`✅ Step 4 Complete: Download data stored for ${slug}`);
        } else {
          console.log(
            `Failed to fetch download data for ${slug}: ${downloadResponse.status}`
          );
        }
      } catch (error) {
        console.error(`Error fetching download data for ${slug}:`, error);
      }

      // STEP 5: Fetch and store plugin keywords from tags
      console.log(`📝 Step 5: Storing plugin keywords for ${slug}`);

      try {
        // Extract keywords from plugin tags
        const pluginTags = pluginData.tags || {};

        // Store keywords directly in pluginkeywordranks collection
        const { default: PluginKeywordRank } = await import(
          "../models/PluginKeywordRank.js"
        );
        await PluginKeywordRank.addKeywordsFromTags(
          slug,
          displayName,
          pluginTags
        );

        console.log(
          `✅ Step 5 Complete: Keywords stored in pluginkeywordranks for ${slug} from ${
            Object.keys(pluginTags).length
          } tags`
        );
      } catch (error) {
        console.error(`Error storing keywords for ${slug}:`, error);
      }

      // STEP 6: Fetch and store plugin reviews from RSS feed
      console.log(`📝 Step 6: Fetching and storing plugin reviews for ${slug}`);

      try {
        // Fetch reviews from WordPress RSS feed
        const reviewsUrl = `https://wordpress.org/support/plugin/${slug}/reviews/feed/`;
        console.log(`🔗 Fetching reviews from: ${reviewsUrl}`);

        const reviewsResponse = await fetch(reviewsUrl, {
          headers: {
            "User-Agent": "WordPress Plugin Dashboard/1.0",
            Accept: "application/rss+xml, application/xml, text/xml",
          },
          // Note: node-fetch doesn't support timeout option, handle with AbortController if needed
        });

        console.log(
          `📡 Reviews response status: ${reviewsResponse.status} for ${slug}`
        );

        if (reviewsResponse.ok) {
          const reviewsXml = await reviewsResponse.text();
          console.log(
            `📄 Reviews XML length: ${reviewsXml.length} characters for ${slug}`
          );

          if (reviewsXml.length < 100) {
            console.log(
              `⚠️ Reviews XML seems too short for ${slug}, content: ${reviewsXml.substring(
                0,
                200
              )}`
            );
          }

          // Parse RSS feed and extract reviews
          const reviewsData = await parseReviewsFromRSS(reviewsXml, slug);

          if (Object.keys(reviewsData).length > 0) {
            // Convert object to array format for new schema
            const reviewsArray = Object.keys(reviewsData).map((reviewId) => ({
              reviewId,
              ...reviewsData[reviewId],
            }));

            // Store reviews in pluginreviews collection
            const storedReviews = await PluginReview.upsertPluginReviews(
              slug,
              displayName,
              reviewsArray
            );

            console.log(
              `✅ Step 6 Complete: Reviews stored for ${slug} (${
                reviewsArray.length
              } reviews) - Database result: ${
                storedReviews ? "Success" : "Failed"
              }`
            );
          } else {
            console.log(`⚠️ No reviews found in RSS feed for ${slug}`);
          }
        } else {
          console.log(
            `❌ Failed to fetch reviews for ${slug}: HTTP ${reviewsResponse.status} ${reviewsResponse.statusText}`
          );

          // Try to get response text for debugging
          try {
            const errorText = await reviewsResponse.text();
            console.log(`Error response body: ${errorText.substring(0, 500)}`);
          } catch (e) {
            console.log(`Could not read error response body: ${e.message}`);
          }
        }
      } catch (error) {
        console.error(`❌ Error fetching reviews for ${slug}:`, error.message);
        console.error(`Full error:`, error);
      }

      console.log(
        `🎉 Complete 6-step plugin addition workflow completed successfully for plugin: ${slug}`
      );

      res.json({
        success: true,
        message: `Plugin "${displayName}" added successfully`,
        plugin: addedPlugin,
      });
    } catch (error) {
      console.error("Add plugin with data error:", error);

      // Handle payload too large errors specifically
      if (error.type === "entity.too.large") {
        return res.status(413).json({
          success: false,
          message:
            "Plugin data is too large. Please try again or contact support.",
          error: "Payload too large",
        });
      }

      res.status(500).json({
        success: false,
        message: "Failed to add plugin",
        error: error.message,
      });
    }
  }
);

// Add plugin to user's list (only admin and superadmin can add)
router.post("/added", authenticateToken, requireAdmin, async (req, res) => {
  try {
    const userId = req.user._id;
    const { slug } = req.body;

    console.log("Add plugin - userId:", userId, "slug:", slug);

    if (!slug) {
      return res.status(400).json({
        success: false,
        message: "Plugin slug is required",
      });
    }

    // TASK 1: Store Plugin in addedplugins Collection
    console.log(
      `🚀 Starting Task 1: Store plugin ${slug} in addedplugins collection`
    );

    // Get plugin data from existing plugins collection to get current rank and icons
    let plugin = await Plugin.findBySlug(slug);
    let currentRank = null;
    let pluginIcons = {};

    if (plugin) {
      if (plugin.currentRank && plugin.currentRank > 0) {
        currentRank = plugin.currentRank;
        console.log(
          `Found plugin ${slug} in plugins collection with rank: ${currentRank}`
        );
      }

      // Get icons from plugins collection
      if (plugin.icons && (plugin.icons["1x"] || plugin.icons["2x"])) {
        pluginIcons = plugin.icons;
        console.log(
          `Found icons for plugin ${slug} in plugins collection:`,
          pluginIcons
        );
      }
    } else {
      console.log(
        `Plugin ${slug} not found in plugins collection or no rank available`
      );

      // If plugin not found in database, try to fetch it from WordPress API
      try {
        console.log(
          `Fetching plugin ${slug} from WordPress API to get rank...`
        );

        // Fetch plugin info from WordPress API
        const wpApiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${slug}&request[fields][icons]=true`;
        const wpResponse = await fetch(wpApiUrl);

        if (wpResponse.ok) {
          const wpData = await wpResponse.json();

          // Try to find the actual rank by searching through WordPress API pages
          let rank = null;

          try {
            console.log(`Searching for actual rank of plugin: ${slug}`);

            // Search through pages to find the plugin's actual rank
            let found = false;
            let currentPage = 1;
            const maxPagesToSearch = 100; // Limit search to first 10,000 plugins (100 pages * 100 per page)

            while (!found && currentPage <= maxPagesToSearch) {
              const searchUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[page]=${currentPage}&request[per_page]=100`;
              const searchResponse = await fetch(searchUrl);

              if (searchResponse.ok) {
                const searchData = await searchResponse.json();
                if (searchData.plugins && searchData.plugins.length > 0) {
                  const pluginIndex = searchData.plugins.findIndex(
                    (p) => p.slug === slug
                  );
                  if (pluginIndex !== -1) {
                    rank = (currentPage - 1) * 100 + pluginIndex + 1;
                    found = true;
                    console.log(
                      `Found plugin ${slug} at rank: ${rank} (page ${currentPage}, position ${
                        pluginIndex + 1
                      })`
                    );
                    break;
                  }
                }
              }

              currentPage++;

              // Add small delay to be respectful to the API
              if (currentPage <= maxPagesToSearch) {
                await new Promise((resolve) => setTimeout(resolve, 100));
              }
            }

            if (!found) {
              console.log(
                `Plugin ${slug} not found in first ${
                  maxPagesToSearch * 100
                } plugins, rank will be set during bulk fetch`
              );
            }
          } catch (rankError) {
            console.log("Could not fetch rank:", rankError.message);
          }

          // Create plugin in database
          plugin = new Plugin({
            slug: wpData.slug,
            name: wpData.name,
            currentRank: rank,
            short_description: wpData.short_description || "",
            lastFetched: new Date(),
            fetchCount: 1,
          });

          await plugin.save();
          console.log(`Plugin ${slug} fetched and saved to plugins collection`);
          currentRank = rank;
        } else {
          return res.status(404).json({
            success: false,
            message: `Plugin "${slug}" not found in WordPress repository.`,
          });
        }
      } catch (fetchError) {
        console.error("Error fetching plugin from WordPress API:", fetchError);
        return res.status(404).json({
          success: false,
          message: `Plugin "${slug}" not found and could not be fetched from WordPress repository.`,
        });
      }
    }

    // Truncate plugin name for display
    const displayName = plugin.name.split(/[-–:]|&#8211;/)[0].trim();

    // Store plugin in addedplugins collection with rankHistory and icons
    const addedPlugin = await AddedPlugin.addPluginForUser(userId, {
      slug: plugin.slug,
      name: plugin.name,
      displayName,
      currentRank: currentRank,
      rankGrowth: null, // Will be calculated later when we have more history
      short_description: plugin.short_description,
      icons: pluginIcons, // Icons from plugins collection
    });

    if (!addedPlugin) {
      return res.status(500).json({
        success: false,
        message: "Failed to store plugin in addedplugins collection",
      });
    }

    console.log(
      `✅ Task 1 Complete: Plugin "${displayName}" stored in addedplugins collection with rankHistory`
    );

    // TASK 2: Fetch and Store Plugin Information
    console.log(
      `🚀 Starting Task 2: Fetch detailed plugin information for ${plugin.slug} from WordPress API`
    );

    try {
      const wpApiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${plugin.slug}&request[fields][icons]=true`;
      const wpResponse = await fetch(wpApiUrl);

      if (!wpResponse.ok) {
        throw new Error(`WordPress API returned status: ${wpResponse.status}`);
      }

      const wpData = await wpResponse.json();

      if (wpData.error) {
        throw new Error(`WordPress API error: ${wpData.error}`);
      }

      // Store detailed plugin information in plugininformations collection with icons
      await PluginInformation.upsertPluginInformation(
        plugin.slug,
        displayName,
        wpData,
        currentRank,
        pluginIcons
      );

      console.log(
        `✅ Task 2 Complete: Detailed plugin information stored in plugininformations collection for ${plugin.slug}`
      );
    } catch (infoError) {
      console.error(
        `❌ Task 2 Failed: Error fetching detailed plugin information for ${plugin.slug}:`,
        infoError
      );

      // Return error if Task 2 fails - this is critical for the workflow
      return res.status(500).json({
        success: false,
        message: `Failed to fetch detailed plugin information: ${infoError.message}`,
      });
    }

    // TASK 3: Plugin is now ready to be displayed on dashboard
    console.log(
      `✅ Task 3 Complete: Plugin "${displayName}" is ready to be displayed on dashboard`
    );
    console.log(`📊 Plugin data will be pulled from multiple collections:`);
    console.log(
      `   - Plugin Rank: addedplugins.rankHistory (most recent entry)`
    );
    console.log(`   - Version: plugininformations collection`);
    console.log(`   - Download Trends: plugindownloaddatas collection`);

    // All 3 tasks completed successfully - return success response

    res.json({
      success: true,
      message: `Plugin "${displayName}" added successfully!`,
      addedPlugin,
    });
  } catch (error) {
    console.error("Add plugin error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to add plugin",
      error: error.message,
    });
  }
});

// Comprehensive refresh plugin data from WordPress API
router.post(
  "/added/:slug/refresh",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { slug } = req.params;

      console.log(
        "Comprehensive refresh plugin - userId:",
        userId,
        "slug:",
        slug
      );

      // Get added plugin from any admin/superadmin user
      const User = (await import("../models/User.js")).default;
      const adminUsers = await User.find({
        role: { $in: ["admin", "superadmin"] },
        isActive: true,
      }).select("_id");

      const adminUserIds = adminUsers.map((user) => user._id);

      const addedPlugin = await AddedPlugin.findOne({
        userId: { $in: adminUserIds },
        pluginSlug: slug,
        isActive: true,
      });

      if (!addedPlugin) {
        return res.status(404).json({
          success: false,
          message: "Added plugin not found",
        });
      }

      // Fetch fresh plugin data from WordPress API
      const pluginApiUrl = `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${slug}&request[fields][icons]=true`;
      const pluginResponse = await fetch(pluginApiUrl);

      if (!pluginResponse.ok) {
        throw new Error(`WordPress API error: ${pluginResponse.status}`);
      }

      const pluginData = await pluginResponse.json();
      const displayName = pluginData.name.split(/[-–:]|&#8211;/)[0].trim();

      // Update plugin data in database
      const updatedPlugin = await Plugin.findOneAndUpdate(
        { slug: slug },
        {
          slug: slug,
          name: pluginData.name,
          currentRank: null, // Will be updated below
          short_description: pluginData.short_description || "",
          icons: pluginData.icons || {},
          lastFetched: new Date(),
          $inc: { fetchCount: 1 },
        },
        { upsert: true, new: true }
      );

      // Phase 2: Fetch current rank from plugins collection and compare with addedplugins
      console.log(`🔄 Phase 2: Fetching current rank for plugin ${slug}...`);

      // Get the added plugin document first
      const addedPluginDoc = await AddedPlugin.findOne({
        userId: { $in: adminUserIds },
        pluginSlug: slug,
        isActive: true,
      });

      if (!addedPluginDoc) {
        console.log(`❌ Added plugin document not found for ${slug}`);
        return res.status(404).json({
          success: false,
          message: "Added plugin not found",
        });
      }

      // Get the current rank and icons from the updated plugin (use the fresh data we just updated)
      let pluginsCollectionRank = updatedPlugin?.currentRank || null;
      let pluginsCollectionIcons = updatedPlugin?.icons || {};

      console.log(
        `📦 Using updated plugin data from plugins collection for ${slug}:`,
        {
          rank: pluginsCollectionRank,
          hasIcons: Object.keys(pluginsCollectionIcons).length > 0,
          icons: pluginsCollectionIcons,
        }
      );

      if (Object.keys(pluginsCollectionIcons).length === 0) {
        console.log(
          `⚠️ No icons found in updated plugin data for ${slug}. Icons will be empty in updated collections.`
        );
      }

      // If no rank in plugins database, try to fetch from WordPress API
      if (!pluginsCollectionRank) {
        try {
          console.log(
            `Searching for rank of plugin ${slug} in WordPress API...`
          );

          // Search through pages to find the plugin's actual rank
          let found = false;
          let currentPage = 1;
          const maxPagesToSearch = 100; // Limit search to first 10,000 plugins

          while (!found && currentPage <= maxPagesToSearch) {
            const searchUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[page]=${currentPage}&request[per_page]=100`;
            const searchResponse = await fetch(searchUrl);

            if (searchResponse.ok) {
              const searchData = await searchResponse.json();
              if (searchData.plugins && searchData.plugins.length > 0) {
                const pluginIndex = searchData.plugins.findIndex(
                  (p) => p.slug === slug
                );
                if (pluginIndex !== -1) {
                  pluginsCollectionRank =
                    (currentPage - 1) * 100 + pluginIndex + 1;
                  found = true;
                  console.log(
                    `Found plugin ${slug} at rank: ${pluginsCollectionRank}`
                  );

                  // Update the plugins collection with the new rank
                  await Plugin.findOneAndUpdate(
                    { slug: slug },
                    { currentRank: pluginsCollectionRank },
                    { new: true }
                  );
                  break;
                }
              }
            }

            currentPage++;
            // Add small delay to be respectful to the API
            if (currentPage <= maxPagesToSearch) {
              await new Promise((resolve) => setTimeout(resolve, 100));
            }
          }

          if (!found) {
            console.log(
              `Plugin ${slug} not found in first ${
                maxPagesToSearch * 100
              } plugins`
            );
          }
        } catch (rankError) {
          console.error(`Error fetching rank for ${slug}:`, rankError.message);
        }
      }

      // Get current ranks from both collections for comparison
      const addedPluginsCurrentRank = addedPluginDoc.currentRank;

      // Get plugin information from plugininformations collection
      const pluginInfoDoc = await PluginInformation.findOne({
        pluginSlug: slug,
        isActive: true,
      });

      const pluginInfoCurrentRank = pluginInfoDoc?.currentRank || null;

      console.log(`🔍 Rank comparison for ${slug}:`);
      console.log(`   - Plugins collection rank: ${pluginsCollectionRank}`);
      console.log(
        `   - AddedPlugins collection rank: ${addedPluginsCurrentRank}`
      );
      console.log(
        `   - PluginInformations collection rank: ${pluginInfoCurrentRank}`
      );

      if (pluginsCollectionRank !== null) {
        // Check if ranks are different in either collection
        const addedPluginsRankChanged =
          addedPluginsCurrentRank !== pluginsCollectionRank;
        const pluginInfoRankChanged =
          pluginInfoCurrentRank !== pluginsCollectionRank;

        if (!addedPluginsRankChanged && !pluginInfoRankChanged) {
          // No rank changes - just update icons and timestamps
          console.log(
            `✅ No rank change for ${slug} - ranks are equal (${pluginsCollectionRank})`
          );

          // Update addedplugins collection
          addedPluginDoc.icons = pluginsCollectionIcons;
          addedPluginDoc.lastUpdated = new Date();
          await addedPluginDoc.save();

          console.log(
            `🖼️ Icons updated in addedplugins collection for ${slug}:`,
            pluginsCollectionIcons
          );

          // Also update plugininformations collection icons even if rank hasn't changed
          if (pluginInfoDoc) {
            pluginInfoDoc.icons = pluginsCollectionIcons;
            pluginInfoDoc.fetchedAt = new Date();
            await pluginInfoDoc.save();

            console.log(
              `🖼️ Icons updated in plugininformations collection for ${slug}:`,
              pluginsCollectionIcons
            );
          }

          console.log(
            `✅ Phase 2 Complete: Plugin ${slug} refreshed (no rank change, icons updated in both collections)`
          );
        } else {
          // Ranks are different - update both collections
          console.log(`🔄 Rank changes detected for ${slug}:`);

          if (addedPluginsRankChanged) {
            console.log(
              `   - AddedPlugins: ${addedPluginsCurrentRank} → ${pluginsCollectionRank}`
            );
            await addedPluginDoc.updateRankWithHistory(pluginsCollectionRank);
            addedPluginDoc.icons = pluginsCollectionIcons;
            await addedPluginDoc.save();

            console.log(
              `🖼️ Icons updated in addedplugins collection for ${slug}:`,
              pluginsCollectionIcons
            );
          }

          if (pluginInfoRankChanged && pluginInfoDoc) {
            console.log(
              `   - PluginInformations: ${pluginInfoCurrentRank} → ${pluginsCollectionRank}`
            );

            // Use the updateRankWithHistory method for proper rank history tracking
            await pluginInfoDoc.updateRankWithHistory(pluginsCollectionRank);

            // Calculate rank growth for plugininformations
            const rankGrowth =
              pluginInfoCurrentRank !== null
                ? pluginInfoCurrentRank - pluginsCollectionRank
                : 0;

            // Update other fields
            pluginInfoDoc.rankGrowth = rankGrowth;
            pluginInfoDoc.icons = pluginsCollectionIcons;
            await pluginInfoDoc.save();

            console.log(
              `🖼️ Icons updated in plugininformations collection for ${slug}:`,
              pluginsCollectionIcons
            );
          }

          console.log(
            `✅ Phase 2 Complete: Rank history and icons updated for plugin ${slug} in both collections`
          );
        }
      } else {
        // No rank available from plugins collection
        console.log(`⚠️ No rank available for ${slug} from plugins collection`);

        // Update icons and timestamps in both collections
        addedPluginDoc.icons = pluginsCollectionIcons;
        addedPluginDoc.lastUpdated = new Date();
        await addedPluginDoc.save();

        console.log(
          `🖼️ Icons updated in addedplugins collection for ${slug}:`,
          pluginsCollectionIcons
        );

        if (pluginInfoDoc) {
          pluginInfoDoc.icons = pluginsCollectionIcons;
          pluginInfoDoc.fetchedAt = new Date();
          await pluginInfoDoc.save();

          console.log(
            `🖼️ Icons updated in plugininformations collection for ${slug}:`,
            pluginsCollectionIcons
          );
        }

        console.log(
          `✅ Phase 2 Complete: Plugin ${slug} refreshed (no rank available, icons updated in both collections)`
        );
      }

      // Step 2: Refresh detailed plugin information in plugininformations collection
      try {
        console.log(
          `Step 2 Refetch: Refreshing detailed plugin information for ${slug}`
        );

        // Store/update detailed plugin information in plugininformations collection
        // Note: Rank and icons are already updated above if they changed
        // Use updateRankHistory=false to prevent overwriting the rank history we just updated
        await PluginInformation.upsertPluginInformation(
          slug,
          displayName,
          pluginData,
          pluginsCollectionRank,
          pluginsCollectionIcons,
          false // Don't update rank history as it's already handled above
        );

        console.log(
          `✅ Step 2 Refetch Complete: Detailed plugin information updated for ${slug}`
        );
      } catch (infoError) {
        console.error(
          `❌ Step 2 Refetch Failed: Error updating detailed plugin information for ${slug}:`,
          infoError
        );
        // Don't fail the refresh if detailed info update fails
      }

      // Comprehensive data refresh using utility functions - Execute all 5 tasks
      console.log(`Starting comprehensive data refresh for ${slug}...`);

      // Task 2: Refresh keywords from plugin tags (with proper refetch behavior)
      if (pluginData.tags && typeof pluginData.tags === "object") {
        try {
          console.log(`Task 2 Refetch: Refreshing keywords for plugin ${slug}`);

          const { default: PluginKeywordRank } = await import(
            "../models/PluginKeywordRank.js"
          );
          await PluginKeywordRank.refreshKeywordsFromTags(
            slug,
            displayName,
            pluginData.tags
          );
          console.log(
            `✅ Task 2 Refetch Complete: Updated keywords in pluginkeywordranks for plugin ${slug}`
          );
        } catch (keywordError) {
          console.error(
            `❌ Task 2 Refetch Failed: Error updating keywords for ${slug}:`,
            keywordError
          );
        }
      } else {
        console.log(
          `⚠️ Task 2 Refetch Skipped: No tags found for plugin ${slug}`
        );
      }

      // Task 3: Refresh download data
      try {
        console.log(
          `Task 3 Refetch: Refreshing download data for plugin ${slug}`
        );
        await fetchPluginDownloadData(slug, displayName);
        console.log(
          `✅ Task 3 Refetch Complete: Download data updated for plugin ${slug}`
        );
      } catch (downloadError) {
        console.error(
          `❌ Task 3 Refetch Failed: Error fetching download data for ${slug}:`,
          downloadError
        );
      }

      // Task 4: Refresh reviews
      try {
        console.log(`Task 4 Refetch: Refreshing reviews for plugin ${slug}`);
        await fetchPluginReviews(slug, displayName);
        console.log(
          `✅ Task 4 Refetch Complete: Reviews updated for plugin ${slug}`
        );
      } catch (reviewError) {
        console.error(
          `❌ Task 4 Refetch Failed: Error fetching reviews for ${slug}:`,
          reviewError
        );
      }

      // Task 5: Refresh versions (DISABLED)
      // NOTE: Plugin versions functionality is temporarily disabled to reduce API calls and database operations
      /*
      try {
        console.log(`Task 5 Refetch: Refreshing versions for plugin ${slug}`);
        await fetchPluginVersions(slug, displayName, null); // Fetch from API since we don't store full plugin data
        console.log(
          `✅ Task 5 Refetch Complete: Versions updated for plugin ${slug}`
        );
      } catch (versionError) {
        console.error(
          `❌ Task 5 Refetch Failed: Error fetching versions for ${slug}:`,
          versionError
        );
      }
      */
      console.log(
        `⚠️ Task 5 Refetch Skipped: Plugin versions functionality is temporarily disabled`
      );

      // 5. Refresh keyword ranks for existing keywords
      let updatedCurrentRank = null;
      try {
        const { default: PluginKeywordRank } = await import(
          "../models/PluginKeywordRank.js"
        );
        const pluginKeywords = await PluginKeywordRank.getPluginKeywordRanks(
          slug
        );
        if (pluginKeywords && pluginKeywords.length > 0) {
          const today = new Date();
          const formattedDate = `${today
            .getDate()
            .toString()
            .padStart(2, "0")}-${(today.getMonth() + 1)
            .toString()
            .padStart(2, "0")}-${today.getFullYear()}`;

          for (const keywordRecord of pluginKeywords) {
            const keyword = keywordRecord.keyword;
            const keywordSource = keywordRecord.source || "default";
            try {
              const keywordRankUrl = `https://api.wordpress.org/plugins/info/1.2/?action=query_plugins&request[search]=${encodeURIComponent(
                keyword
              )}`;
              const keywordResponse = await fetch(keywordRankUrl);

              if (keywordResponse.ok) {
                const keywordApiData = await keywordResponse.json();
                if (
                  keywordApiData.plugins &&
                  keywordApiData.plugins.length > 0
                ) {
                  const pluginIndex = keywordApiData.plugins.findIndex(
                    (p) => p.slug === slug
                  );
                  if (pluginIndex !== -1) {
                    const newRank = pluginIndex + 1;
                    await PluginKeywordRank.upsertKeywordRank(
                      slug,
                      displayName,
                      keyword,
                      newRank,
                      formattedDate,
                      keywordSource
                    );
                    // Update the current rank if this is the first keyword or a better rank
                    if (
                      updatedCurrentRank === null ||
                      newRank < updatedCurrentRank
                    ) {
                      updatedCurrentRank = newRank;
                    }
                  }
                }
              }
            } catch (keywordError) {
              console.log(
                `Could not update keyword rank for ${keyword}:`,
                keywordError.message
              );
            }
          }
        }
      } catch (keywordError) {
        console.log("Could not update keyword ranks:", keywordError.message);
      }

      console.log(`✅ Comprehensive refresh completed for ${slug}`);

      res.json({
        success: true,
        message: "Plugin refreshed successfully",
        currentRank: updatedCurrentRank,
      });
    } catch (error) {
      console.error("Refresh plugin error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to refresh plugin",
        error: error.message,
      });
    }
  }
);

// Remove plugin from added plugins (admin/superadmin only)
router.delete(
  "/added/:slug",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const { slug } = req.params;
      console.log(`🗑️ DELETE request received for plugin slug: ${slug}`);

      // Find and remove the added plugin from any admin/superadmin user
      const User = (await import("../models/User.js")).default;
      const adminUsers = await User.find({
        role: { $in: ["admin", "superadmin"] },
        isActive: true,
      }).select("_id");

      const adminUserIds = adminUsers.map((user) => user._id);
      console.log(`👥 Found ${adminUsers.length} admin users`);

      // First, let's check if the plugin exists
      const existingPlugin = await AddedPlugin.findOne({
        userId: { $in: adminUserIds },
        pluginSlug: slug,
        isActive: true,
      });

      console.log(`🔍 Existing plugin found:`, existingPlugin ? "YES" : "NO");
      if (existingPlugin) {
        console.log(
          `📦 Plugin details: ${existingPlugin.pluginName} (${existingPlugin.pluginSlug})`
        );
      }

      const result = await AddedPlugin.findOneAndUpdate(
        {
          userId: { $in: adminUserIds },
          pluginSlug: slug,
          isActive: true,
        },
        {
          isActive: false,
          removedAt: new Date(),
        },
        { new: true }
      );

      if (!result) {
        console.log(`❌ Plugin not found for deletion: ${slug}`);
        return res.status(404).json({
          success: false,
          message: "Added plugin not found",
        });
      }

      console.log(
        `✅ Plugin successfully marked as inactive: ${result.pluginName}`
      );

      res.json({
        success: true,
        message: "Plugin removed successfully",
      });
    } catch (error) {
      console.error("Remove added plugin error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to remove plugin",
        error: error.message,
      });
    }
  }
);

// Get added plugins with rank history for charts (accessible to all authenticated users - no userID filtering)
router.get("/added/history", authenticateToken, async (req, res) => {
  try {
    const { days = 30, slugs } = req.query;

    // Get ALL plugins from addedplugins collection - no userID filtering
    let addedPlugins;
    if (slugs) {
      const slugArray = slugs.split(",");
      addedPlugins = await AddedPlugin.find({
        pluginSlug: { $in: slugArray },
        isActive: true,
      });
    } else {
      addedPlugins = await AddedPlugin.find({
        isActive: true,
      });
    }

    // Process rank history for each plugin - data is already in addedplugins collection
    const pluginsWithHistory = addedPlugins.map((addedPlugin) => {
      const pluginObj = addedPlugin.toObject();

      // Filter rank history by days if specified
      let filteredRankHistory = pluginObj.rankHistory || [];

      if (days && days > 0) {
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - parseInt(days));

        filteredRankHistory = filteredRankHistory.filter((entry) => {
          // Parse date in dd-mm-yyyy format
          const [day, month, year] = entry.date.split("-");
          const entryDate = new Date(year, month - 1, day);
          return entryDate >= cutoffDate;
        });
      }

      return {
        ...pluginObj,
        rankHistory: filteredRankHistory,
      };
    });

    // Add metadata about data availability
    const pluginsWithData = pluginsWithHistory.filter(
      (p) => p.rankHistory && p.rankHistory.length > 0
    );

    res.json({
      success: true,
      plugins: pluginsWithHistory,
      totalPlugins: addedPlugins.length,
      pluginsWithRankHistory: pluginsWithData.length,
      message:
        "Plugin rank history retrieved successfully (no userID filtering)",
      metadata: {
        requestedBy: req.user.email,
        userRole: req.user.role,
        accessibleToAllUsers: true,
        noUserFiltering: true,
        source: "ALL plugins from wpdev_plugin.addedplugins collection",
      },
    });
  } catch (error) {
    console.error("Get added plugins history error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get plugins history",
      error: error.message,
    });
  }
});

// Get all plugins from plugininformations collection for Plugin Rank page
router.get("/rank/all", authenticateToken, async (req, res) => {
  try {
    const {
      sortBy = "pluginName",
      sortOrder = "asc",
      limit = 100,
      page = 1,
    } = req.query;

    const limitNum = parseInt(limit);
    const pageNum = parseInt(page);
    const skip = (pageNum - 1) * limitNum;

    // Get ALL plugins from plugininformations collection
    const PluginInformation = (await import("../models/PluginInformation.js"))
      .default;

    const plugins = await PluginInformation.find({
      isActive: true,
    })
      .sort({ [sortBy]: sortOrder === "desc" ? -1 : 1 })
      .skip(skip)
      .limit(limitNum)
      .select(
        "pluginSlug pluginName displayName currentRank rankHistory icons version rating numRatings"
      );

    // Get total count for pagination
    const totalCount = await PluginInformation.countDocuments({
      isActive: true,
    });

    // Count plugins with rank history
    const pluginsWithRankHistory = plugins.filter(
      (p) => p.rankHistory && p.rankHistory.length > 0
    );

    res.json({
      success: true,
      plugins: plugins,
      totalPlugins: plugins.length,
      pluginsWithRankHistory: pluginsWithRankHistory.length,
      totalCount,
      currentPage: pageNum,
      totalPages: Math.ceil(totalCount / limitNum),
      message:
        "All plugins from plugininformations collection - accessible to all authenticated users",
      metadata: {
        requestedBy: req.user.email,
        userRole: req.user.role,
        accessibleToAllUsers: true,
        noUserFiltering: true,
        source: "wpdev_plugin.plugininformations collection",
      },
    });
  } catch (error) {
    console.error("Get plugins from plugininformations error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get plugins from plugininformations collection",
      error: error.message,
    });
  }
});

// Test endpoint to verify plugin rank access for all users (no userID filtering)
router.get("/added/access-test", authenticateToken, async (req, res) => {
  try {
    // Get ALL plugins from addedplugins collection - no userID filtering
    const addedPlugins = await AddedPlugin.find({
      isActive: true,
    }).select("pluginSlug pluginName userId rankHistory");

    // Count plugins with rank history
    const pluginsWithRankHistory = addedPlugins.filter(
      (p) => p.rankHistory && p.rankHistory.length > 0
    );

    res.json({
      success: true,
      message:
        "Plugin rank access test - ALL plugins accessible (no userID filtering)",
      testResults: {
        currentUser: {
          email: req.user.email,
          role: req.user.role,
          canAccessAllPlugins: true,
        },
        dataSource: "wpdev_plugin.addedplugins collection",
        filteringPolicy: "No userID filtering - ALL plugins accessible",
        pluginStats: {
          totalPluginsInDatabase: addedPlugins.length,
          pluginsWithRankHistory: pluginsWithRankHistory.length,
          pluginsWithoutRankHistory:
            addedPlugins.length - pluginsWithRankHistory.length,
        },
        samplePlugins: addedPlugins.slice(0, 5).map((p) => ({
          slug: p.pluginSlug,
          name: p.pluginName,
          addedByUserId: p.userId,
          hasRankHistory: p.rankHistory && p.rankHistory.length > 0,
          rankHistoryCount: p.rankHistory ? p.rankHistory.length : 0,
        })),
      },
    });
  } catch (error) {
    console.error("Plugin access test error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to run plugin access test",
      error: error.message,
    });
  }
});

// Get single plugin by slug (MOVED AFTER /added routes)
router.get("/:slug", authenticateToken, async (req, res) => {
  try {
    const { slug } = req.params;

    const plugin = await Plugin.findBySlug(slug);
    if (!plugin) {
      return res.status(404).json({
        success: false,
        message: "Plugin not found",
      });
    }

    // Get rank history for this plugin
    const rankHistory = await PluginRankHistory.getPluginHistory(slug, 30);

    res.json({
      success: true,
      plugin,
      rankHistory,
    });
  } catch (error) {
    console.error("Get plugin error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get plugin",
      error: error.message,
    });
  }
});

// Get plugin rank history
router.get("/:slug/history", authenticateToken, async (req, res) => {
  try {
    const { slug } = req.params;
    const { days = 30 } = req.query;

    const history = await PluginRankHistory.getRankTrend(slug, parseInt(days));

    res.json({
      success: true,
      slug,
      history,
    });
  } catch (error) {
    console.error("Get plugin history error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get plugin history",
      error: error.message,
    });
  }
});

// Get plugin download data (proxy to avoid CORS)
router.get("/:slug/downloads", authenticateToken, async (req, res) => {
  try {
    const { slug } = req.params;

    console.log("Fetching download data for slug:", slug);

    const downloadUrl = `https://api.wordpress.org/stats/plugin/1.0/downloads.php?slug=${slug}`;
    const response = await fetch(downloadUrl);

    if (!response.ok) {
      throw new Error(`WordPress API error: ${response.status}`);
    }

    const downloadData = await response.json();

    res.json({
      success: true,
      slug,
      downloads: downloadData,
    });
  } catch (error) {
    console.error("Get plugin downloads error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to get plugin downloads",
      error: error.message,
    });
  }
});

// Manual trigger for rank tracking update (for testing)
router.post(
  "/added/update-ranks",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      console.log("🔧 Manual trigger: Updating all added plugins ranks...");

      // Import scheduler to use its method
      const pluginScheduler = (await import("../services/scheduler.js"))
        .default;

      // Call the rank update method
      await pluginScheduler.updateAddedPluginsRanksWithHistory();

      res.json({
        success: true,
        message:
          "Added plugins ranks updated successfully with history tracking",
      });
    } catch (error) {
      console.error("Manual rank update error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update added plugins ranks",
        error: error.message,
      });
    }
  }
);

// Save plugin rank history to addedplugins collection
router.post(
  "/added/rank-history",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const userId = req.user._id;
      const { pluginSlug, rank } = req.body;

      if (!pluginSlug || !rank) {
        return res.status(400).json({
          success: false,
          message: "Plugin slug and rank are required",
        });
      }

      // Validate rank value
      if (rank <= 0 || !Number.isInteger(rank)) {
        return res.status(400).json({
          success: false,
          message: "Rank must be a positive integer greater than 0",
        });
      }

      // Find the added plugin for this user
      const addedPlugin = await AddedPlugin.findOne({
        userId,
        pluginSlug: pluginSlug.toLowerCase(),
        isActive: true,
      });

      if (!addedPlugin) {
        return res.status(404).json({
          success: false,
          message: "Plugin not found in your added plugins",
        });
      }

      // Update rank with history tracking
      await addedPlugin.updateRankWithHistory(rank);

      res.json({
        success: true,
        message: `Plugin rank updated: #${rank}`,
        currentRank: rank,
        pluginSlug: pluginSlug,
      });
    } catch (error) {
      console.error("Save added plugin rank history error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to save rank history",
        error: error.message,
      });
    }
  }
);

// Save plugin rank history (legacy endpoint - keeping for backward compatibility)
router.post(
  "/rank-history",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      const { pluginSlug, pluginName, rank, date } = req.body;

      if (!pluginSlug || !pluginName || !rank || !date) {
        return res.status(400).json({
          success: false,
          message: "Plugin slug, name, rank, and date are required",
        });
      }

      // Validate rank value
      if (rank <= 0 || !Number.isInteger(rank)) {
        return res.status(400).json({
          success: false,
          message: "Rank must be a positive integer greater than 0",
        });
      }

      // Format date as dd-mm-yyyy
      const dateObj = new Date(date);
      const formattedDate = `${dateObj
        .getDate()
        .toString()
        .padStart(2, "0")}-${(dateObj.getMonth() + 1)
        .toString()
        .padStart(2, "0")}-${dateObj.getFullYear()}`;

      // Upsert rank history (update if exists for same date, insert if not)
      const existingEntry = await PluginRankHistory.findOne({
        $or: [
          { pluginSlug: pluginSlug.toLowerCase(), date: formattedDate },
          { slug: pluginSlug.toLowerCase(), date: formattedDate },
        ],
      });

      if (existingEntry) {
        // Update existing entry
        existingEntry.rank = rank;
        existingEntry.fetchDate = new Date();
        // Ensure both slug and pluginSlug are set
        existingEntry.slug = pluginSlug.toLowerCase();
        existingEntry.pluginSlug = pluginSlug.toLowerCase();
        await existingEntry.save();

        res.json({
          success: true,
          message: `Plugin rank updated for ${formattedDate}`,
          rankHistory: existingEntry,
        });
      } else {
        // Create new entry
        const rankHistory = new PluginRankHistory({
          slug: pluginSlug.toLowerCase(), // Set both slug and pluginSlug for compatibility
          pluginSlug: pluginSlug.toLowerCase(),
          pluginName,
          rank,
          date: formattedDate,
          fetchDate: new Date(),
        });

        await rankHistory.save();

        res.json({
          success: true,
          message: `Plugin rank saved for ${formattedDate}`,
          rankHistory,
        });
      }
    } catch (error) {
      console.error("Save rank history error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to save rank history",
        error: error.message,
      });
    }
  }
);

// Clean database - delete all data except users table (superadmin only)
router.post("/clean-database", authenticateToken, async (req, res) => {
  try {
    // Only allow superadmin to clean database
    if (req.user.role !== "superadmin") {
      return res.status(403).json({
        success: false,
        message: "Only superadmin can clean database",
      });
    }

    console.log("Starting database cleanup...");

    // Delete all data from all collections except users
    const deleteResults = {};

    // Delete plugins data
    deleteResults.plugins = await Plugin.deleteMany({});
    console.log(`Deleted ${deleteResults.plugins.deletedCount} plugins`);

    // Delete added plugins data
    deleteResults.addedPlugins = await AddedPlugin.deleteMany({});
    console.log(
      `Deleted ${deleteResults.addedPlugins.deletedCount} added plugins`
    );

    // Delete plugin rank history
    deleteResults.pluginRankHistory = await PluginRankHistory.deleteMany({});
    console.log(
      `Deleted ${deleteResults.pluginRankHistory.deletedCount} rank history records`
    );

    // Delete plugin download data
    deleteResults.pluginDownloadData = await PluginDownloadData.deleteMany({});
    console.log(
      `Deleted ${deleteResults.pluginDownloadData.deletedCount} download data records`
    );

    // Delete plugin reviews
    deleteResults.pluginReviews = await PluginReview.deleteMany({});
    console.log(
      `Deleted ${deleteResults.pluginReviews.deletedCount} plugin reviews`
    );

    // Delete plugin keywords
    deleteResults.pluginKeywords = await PluginKeyword.deleteMany({});
    console.log(
      `Deleted ${deleteResults.pluginKeywords.deletedCount} plugin keywords`
    );

    // Delete settings (except user-specific settings if needed)
    deleteResults.settings = await Settings.deleteMany({});
    console.log(`Deleted ${deleteResults.settings.deletedCount} settings`);

    console.log("Database cleanup completed successfully");

    res.json({
      success: true,
      message: "Database cleaned successfully (users table preserved)",
      deletedCounts: {
        plugins: deleteResults.plugins.deletedCount,
        addedPlugins: deleteResults.addedPlugins.deletedCount,
        pluginRankHistory: deleteResults.pluginRankHistory.deletedCount,
        pluginDownloadData: deleteResults.pluginDownloadData.deletedCount,
        pluginReviews: deleteResults.pluginReviews.deletedCount,
        pluginKeywords: deleteResults.pluginKeywords.deletedCount,
        settings: deleteResults.settings.deletedCount,
      },
    });
  } catch (error) {
    console.error("Database cleanup error:", error);
    res.status(500).json({
      success: false,
      message: "Failed to clean database",
      error: error.message,
    });
  }
});

// Update rank history for all added plugins
router.post(
  "/update-added-plugins-ranks",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      console.log("Starting rank history update for all added plugins...");

      // Get all active added plugins from admin/superadmin users
      const User = (await import("../models/User.js")).default;
      const adminUsers = await User.find({
        role: { $in: ["admin", "superadmin"] },
        isActive: true,
      }).select("_id");

      const adminUserIds = adminUsers.map((user) => user._id);

      const addedPlugins = await AddedPlugin.find({
        userId: { $in: adminUserIds },
        isActive: true,
      }).select("pluginSlug pluginName displayName");

      if (addedPlugins.length === 0) {
        return res.json({
          success: true,
          message: "No added plugins found to update",
          updatedCount: 0,
        });
      }

      console.log(
        `Found ${addedPlugins.length} added plugins to update rank history for`
      );

      let updatedCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each added plugin
      for (const addedPlugin of addedPlugins) {
        try {
          // Get current plugin data from plugins collection
          const plugin = await Plugin.findOne({ slug: addedPlugin.pluginSlug });

          if (plugin && plugin.currentRank && plugin.currentRank > 0) {
            // Create/update rank history entry for today
            const today = new Date();
            const formattedDate = `${today
              .getDate()
              .toString()
              .padStart(2, "0")}-${(today.getMonth() + 1)
              .toString()
              .padStart(2, "0")}-${today.getFullYear()}`;

            await PluginRankHistory.findOneAndUpdate(
              {
                $or: [
                  {
                    pluginSlug: addedPlugin.pluginSlug.toLowerCase(),
                    date: formattedDate,
                  },
                  {
                    slug: addedPlugin.pluginSlug.toLowerCase(),
                    date: formattedDate,
                  },
                ],
              },
              {
                slug: addedPlugin.pluginSlug.toLowerCase(),
                pluginSlug: addedPlugin.pluginSlug.toLowerCase(),
                pluginName: addedPlugin.displayName || addedPlugin.pluginName,
                rank: plugin.currentRank,
                date: formattedDate,
                fetchDate: new Date(),
                downloads: plugin.pluginData?.downloaded || 0,
                rating: plugin.pluginData?.rating || 0,
                numRatings: plugin.pluginData?.num_ratings || 0,
                version: plugin.pluginData?.version || "",
                lastUpdated: plugin.pluginData?.last_updated || "",
                batchId: `added_update_${Date.now()}`,
              },
              { upsert: true, new: true }
            );

            updatedCount++;
            console.log(
              `✅ Updated rank history for ${addedPlugin.pluginSlug} (rank: ${plugin.currentRank})`
            );
          } else {
            console.warn(
              `⚠️ Plugin ${addedPlugin.pluginSlug} not found in plugins collection or has invalid rank`
            );
          }
        } catch (error) {
          errorCount++;
          const errorMsg = `Error updating ${addedPlugin.pluginSlug}: ${error.message}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      res.json({
        success: true,
        message: `Rank history update completed! ${updatedCount} plugins updated, ${errorCount} errors.`,
        updatedCount,
        errorCount,
        totalPlugins: addedPlugins.length,
        errors: errors.slice(0, 10), // Limit error list
      });
    } catch (error) {
      console.error("Update added plugins ranks error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update rank history for added plugins",
        error: error.message,
      });
    }
  }
);

// Update download data for all added plugins
router.post(
  "/update-added-plugins-downloads",
  authenticateToken,
  requireAdmin,
  async (req, res) => {
    try {
      console.log("Starting download data update for all added plugins...");

      // Get all active added plugins from admin/superadmin users
      const User = (await import("../models/User.js")).default;
      const adminUsers = await User.find({
        role: { $in: ["admin", "superadmin"] },
        isActive: true,
      }).select("_id");

      const adminUserIds = adminUsers.map((user) => user._id);

      const addedPlugins = await AddedPlugin.find({
        userId: { $in: adminUserIds },
        isActive: true,
      }).select("pluginSlug pluginName displayName");

      if (addedPlugins.length === 0) {
        return res.json({
          success: true,
          message: "No added plugins found to update download data",
          updatedCount: 0,
        });
      }

      console.log(
        `Found ${addedPlugins.length} added plugins to update download data for`
      );

      let updatedCount = 0;
      let errorCount = 0;
      const errors = [];

      // Process each added plugin
      for (const addedPlugin of addedPlugins) {
        try {
          console.log(`Fetching download data for: ${addedPlugin.pluginSlug}`);

          const downloadApiUrl = `https://api.wordpress.org/stats/plugin/1.0/downloads.php?slug=${addedPlugin.pluginSlug}`;
          const downloadResponse = await fetch(downloadApiUrl);

          if (downloadResponse.ok) {
            const downloadData = await downloadResponse.json();

            // Store complete download data in single entry
            if (downloadData && typeof downloadData === "object") {
              await PluginDownloadData.upsertDownloadData(
                addedPlugin.pluginSlug,
                addedPlugin.displayName || addedPlugin.pluginName,
                downloadData
              );
              updatedCount++;
              console.log(
                `✅ Updated download data for ${addedPlugin.pluginSlug}`
              );
            }
          } else {
            console.warn(
              `⚠️ Could not fetch download data for ${addedPlugin.pluginSlug}: ${downloadResponse.status}`
            );
          }

          // Add small delay between requests to be respectful to the API
          await new Promise((resolve) => setTimeout(resolve, 200));
        } catch (error) {
          errorCount++;
          const errorMsg = `Error updating download data for ${addedPlugin.pluginSlug}: ${error.message}`;
          console.error(errorMsg);
          errors.push(errorMsg);
        }
      }

      res.json({
        success: true,
        message: `Download data update completed! ${updatedCount} plugins updated, ${errorCount} errors.`,
        updatedCount,
        errorCount,
        totalPlugins: addedPlugins.length,
        errors: errors.slice(0, 10), // Limit error list
      });
    } catch (error) {
      console.error("Update added plugins download data error:", error);
      res.status(500).json({
        success: false,
        message: "Failed to update download data for added plugins",
        error: error.message,
      });
    }
  }
);

export default router;
